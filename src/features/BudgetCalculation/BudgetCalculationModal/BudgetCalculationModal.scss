.budget-calculation-modal {
  width: 1225px; // Increased by 675px from original 550px
  max-width: calc(100vw - 40px); // Ensure it doesn't exceed viewport width with some margin

  @media (max-width: $small-mobile-width) {
    width: auto;
    max-width: calc(100vw - 20px); // Smaller margin on mobile
  }

  &.custom-modal__wrap {
    padding: 32px 0 !important;

    @media (max-width: $small-mobile-width) {
      padding: 20px 0 !important;
    }
  }

  .custom-modal {
    &__title {
      margin-bottom: 15px !important;
    }

    &__title-wrap, &__btn-wrap {
      padding: 0 32px !important;

      @media (max-width: $small-mobile-width) {
        padding: 0 20px !important;
      }
    }
  }

  .scrollbar-container {
    padding: 0 32px;

    @media (max-width: $small-mobile-width) {
      max-height: 300px;
      padding: 0 20px;
      margin-right: 15px;
    }
  }
}
