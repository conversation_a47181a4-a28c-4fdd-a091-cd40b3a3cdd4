.budget-components-state {
  margin-top: 30px;
  border-top: 1px solid $light-color-300;
  padding: 20px 0;

  // Ensure horizontal scrolling instead of wrapping on smaller screens
  overflow-x: auto;
  min-width: 0; // Allow flex shrinking

  &__table {
    min-width: 1100px; // Minimum width to maintain table structure

    .MuiTableCell-root,  .MuiTableRow-root:hover {
      background-color: transparent !important;
      border: 0 !important;
      white-space: nowrap; // Prevent text wrapping in cells

      @media (max-width: $small-mobile-width) {
        padding: 5px !important;
      }
    }
  }

  .MuiCircularProgress-root {
    margin-top: 20px;
  }
}
